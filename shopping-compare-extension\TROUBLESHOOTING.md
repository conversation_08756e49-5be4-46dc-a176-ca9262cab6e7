# 插件故障排除指南

## 🔧 插件无响应问题解决方案

如果您点击插件图标没有响应，请按照以下步骤进行排查：

### 1. 检查插件状态

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 找到"多平台商品搜索对比"插件
4. 检查是否有错误提示（红色文字）

### 2. 重新加载插件

1. 在扩展管理页面找到插件
2. 点击"重新加载"按钮（刷新图标）
3. 再次尝试点击插件图标

### 3. 检查开发者模式

1. 确保右上角的"开发者模式"开关已开启
2. 如果未开启，请开启后重新加载插件

### 4. 查看控制台错误

1. 右键点击插件图标
2. 选择"检查弹出式窗口"
3. 在打开的开发者工具中查看Console面板
4. 查看是否有错误信息

### 5. 常见错误及解决方法

#### 错误：图标文件未找到
**解决方案：**
1. 打开 `icons/create-icons.html` 文件
2. 等待图标自动下载
3. 将下载的图标文件放入 `icons` 文件夹
4. 重新加载插件

#### 错误：权限被拒绝
**解决方案：**
1. 检查manifest.json中的权限配置
2. 确保浏览器允许访问相关网站
3. 重新安装插件

#### 错误：popup.html加载失败
**解决方案：**
1. 检查文件路径是否正确
2. 确保所有文件都在正确的位置
3. 检查文件编码是否为UTF-8

### 6. 简化版本功能说明

当前版本已简化为基础功能：

- ✅ **搜索功能**：输入关键词，点击搜索
- ✅ **多平台支持**：京东、淘宝、拼多多
- ✅ **新标签页打开**：在新标签页中显示搜索结果
- ✅ **状态显示**：实时显示操作状态

### 7. 测试步骤

1. **基础测试**
   - 点击插件图标，应该看到弹窗
   - 弹窗显示"插件已准备就绪"

2. **搜索测试**
   - 在搜索框输入"手机"
   - 点击"搜索"按钮
   - 应该打开三个新标签页

3. **状态测试**
   - 观察状态区域的消息变化
   - 应该显示搜索进度

### 8. 如果问题仍然存在

请提供以下信息：

1. Chrome浏览器版本
2. 操作系统版本
3. 控制台错误信息截图
4. 插件安装步骤

### 9. 联系支持

如果以上步骤都无法解决问题，请：

1. 截图错误信息
2. 描述具体操作步骤
3. 提供系统环境信息

---

**注意：** 这是简化测试版本，主要用于验证基本功能。完整的iframe嵌入功能将在基础功能正常后逐步添加。
