// 简化版本的购物对比应用
class ShoppingCompareApp {
    constructor() {
        this.currentLayout = 'grid';
        this.currentKeyword = '';
        this.platforms = {
            jd: {
                name: '京东',
                baseUrl: 'https://search.jd.com/Search',
                searchParam: 'keyword'
            },
            taobao: {
                name: '淘宝',
                baseUrl: 'https://s.taobao.com/search',
                searchParam: 'q'
            },
            pdd: {
                name: '拼多多',
                baseUrl: 'https://mobile.pinduoduo.com/search_result.html',
                searchParam: 'keyword'
            }
        };

        console.log('ShoppingCompareApp 初始化开始');
        this.init();
    }

    init() {
        console.log('开始初始化事件绑定');
        try {
            this.bindEvents();
            this.loadSettings();
            this.showStatus('插件加载成功！');
            console.log('初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showStatus('初始化失败: ' + error.message);
        }
    }

    bindEvents() {
        console.log('绑定事件监听器');

        // 搜索功能
        const searchBtn = document.getElementById('searchBtn');
        const searchInput = document.getElementById('searchInput');
        const layoutSelect = document.getElementById('layoutSelect');

        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.performSearch());
            console.log('搜索按钮事件已绑定');
        }

        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.performSearch();
            });
            console.log('搜索输入框事件已绑定');
        }

        // 布局切换
        if (layoutSelect) {
            layoutSelect.addEventListener('change', (e) => {
                this.switchLayout(e.target.value);
            });
            console.log('布局选择器事件已绑定');
        }
    }

    async loadSettings() {
        try {
            console.log('加载用户设置');
            const result = await chrome.storage.sync.get(['layout', 'lastKeyword']);
            if (result.layout) {
                this.currentLayout = result.layout;
                const layoutSelect = document.getElementById('layoutSelect');
                if (layoutSelect) {
                    layoutSelect.value = result.layout;
                }
            }
            if (result.lastKeyword) {
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.value = result.lastKeyword;
                }
            }
            console.log('设置加载完成');
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    async saveSettings() {
        try {
            await chrome.storage.sync.set({
                layout: this.currentLayout,
                lastKeyword: this.currentKeyword
            });
            console.log('设置已保存');
        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }

    showStatus(message) {
        console.log('状态:', message);
        const statusElement = document.getElementById('statusMessage');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    performSearch() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) {
            this.showStatus('搜索框未找到');
            return;
        }

        const keyword = searchInput.value.trim();
        if (!keyword) {
            this.showStatus('请输入搜索关键词');
            return;
        }

        this.currentKeyword = keyword;
        this.saveSettings();

        console.log('开始搜索:', keyword);
        this.showStatus(`正在搜索: ${keyword}`);

        // 打开新标签页进行搜索
        Object.keys(this.platforms).forEach(platform => {
            const config = this.platforms[platform];
            const searchUrl = `${config.baseUrl}?${config.searchParam}=${encodeURIComponent(keyword)}`;
            chrome.tabs.create({ url: searchUrl });
        });

        this.showStatus('已在新标签页中打开搜索结果');
    }

    switchLayout(layout) {
        this.currentLayout = layout;
        console.log('切换布局到:', layout);
        this.showStatus(`已切换到${layout === 'grid' ? '网格' : '标签'}布局`);
        this.saveSettings();
    }

}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始初始化应用');
    try {
        new ShoppingCompareApp();
    } catch (error) {
        console.error('应用初始化失败:', error);
    }
});
