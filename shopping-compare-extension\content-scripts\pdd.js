// 拼多多网站内容脚本
class PddContentScript {
    constructor() {
        this.platform = 'pdd';
        this.searchSelectors = {
            searchBox: '.search-input',
            searchButton: '.search-button',
            searchForm: '.search-form'
        };
        this.init();
    }

    init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupContentScript();
            });
        } else {
            this.setupContentScript();
        }
    }

    setupContentScript() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });

        // 优化页面显示
        this.optimizePageForIframe();

        // 监听搜索框变化
        this.monitorSearchBox();

        console.log('拼多多内容脚本已加载');
    }

    handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'performSearch':
                this.performSearch(request.keyword, sendResponse);
                break;
            case 'getPageInfo':
                this.getPageInfo(sendResponse);
                break;
            case 'optimizePage':
                this.optimizePageForIframe();
                sendResponse({ success: true });
                break;
            default:
                sendResponse({ error: '未知操作' });
        }
    }

    performSearch(keyword, sendResponse) {
        try {
            // 拼多多移动版的搜索框选择器
            const searchSelectors = [
                '.search-input',
                'input[placeholder*="搜索"]',
                'input[type="search"]',
                '.search-bar input'
            ];
            
            let searchBox = null;
            for (const selector of searchSelectors) {
                searchBox = document.querySelector(selector);
                if (searchBox) break;
            }

            if (searchBox) {
                // 清空并输入新关键词
                searchBox.value = '';
                searchBox.focus();
                
                // 模拟用户输入
                this.simulateTyping(searchBox, keyword);
                
                // 触发搜索
                setTimeout(() => {
                    const searchButton = document.querySelector('.search-button') ||
                                       document.querySelector('.search-btn') ||
                                       document.querySelector('button[type="submit"]');
                    
                    if (searchButton) {
                        searchButton.click();
                    } else {
                        // 尝试提交表单
                        const form = searchBox.closest('form');
                        if (form) {
                            form.submit();
                        } else {
                            // 按回车键
                            const enterEvent = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                bubbles: true
                            });
                            searchBox.dispatchEvent(enterEvent);
                        }
                    }
                    
                    sendResponse({ 
                        success: true, 
                        message: '搜索已执行',
                        platform: this.platform 
                    });
                }, 500);
            } else {
                sendResponse({ 
                    success: false, 
                    error: '未找到搜索框',
                    platform: this.platform 
                });
            }
        } catch (error) {
            console.error('拼多多搜索执行失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message,
                platform: this.platform 
            });
        }
    }

    simulateTyping(element, text) {
        element.value = '';
        
        for (let i = 0; i < text.length; i++) {
            setTimeout(() => {
                element.value += text[i];
                
                // 触发各种事件
                const events = ['input', 'change', 'keyup'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    element.dispatchEvent(event);
                });
            }, i * 50);
        }
    }

    getPageInfo(sendResponse) {
        try {
            const info = {
                platform: this.platform,
                url: window.location.href,
                title: document.title,
                isSearchPage: this.isSearchPage(),
                currentKeyword: this.getCurrentKeyword(),
                productCount: this.getProductCount(),
                hasResults: this.hasSearchResults()
            };
            
            sendResponse({ success: true, info });
        } catch (error) {
            console.error('获取页面信息失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message 
            });
        }
    }

    isSearchPage() {
        return window.location.href.includes('search_result') || 
               window.location.href.includes('search');
    }

    getCurrentKeyword() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('keyword') || urlParams.get('q') || '';
        } catch (error) {
            return '';
        }
    }

    getProductCount() {
        try {
            // 拼多多搜索结果数量
            const countSelectors = [
                '.result-count',
                '.search-result-count',
                '.total-count'
            ];
            
            for (const selector of countSelectors) {
                const countElement = document.querySelector(selector);
                if (countElement) {
                    const text = countElement.textContent;
                    const match = text.match(/(\d+)/);
                    if (match) {
                        return parseInt(match[1]);
                    }
                }
            }
            
            // 如果没有找到计数，尝试计算商品数量
            const products = document.querySelectorAll('.goods-item, .product-item, [data-goods-id]');
            return products.length;
        } catch (error) {
            return 0;
        }
    }

    hasSearchResults() {
        const resultSelectors = [
            '.goods-item',
            '.product-item',
            '[data-goods-id]',
            '.goods-list .item'
        ];
        
        return resultSelectors.some(selector => 
            document.querySelectorAll(selector).length > 0
        );
    }

    optimizePageForIframe() {
        try {
            // 移除或隐藏不必要的元素
            const elementsToHide = [
                '.header',       // 头部
                '.footer',       // 底部
                '.nav',          // 导航
                '.ad',          // 广告
                '.popup',       // 弹窗
                '.float-layer', // 浮层
                '.download-app', // 下载APP提示
                '.login-guide', // 登录引导
                '.toolbar',     // 工具栏
                '.top-bar'      // 顶部栏
            ];

            elementsToHide.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.style.display = 'none';
                });
            });

            // 调整移动端页面样式以适应iframe
            const style = document.createElement('style');
            style.textContent = `
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    min-width: auto !important;
                    font-size: 12px !important;
                }
                .container, .main {
                    width: 100% !important;
                    min-width: auto !important;
                    margin: 0 !important;
                    padding: 5px !important;
                }
                .goods-list {
                    width: 100% !important;
                    padding: 0 !important;
                }
                .goods-item {
                    margin: 2px !important;
                    font-size: 11px !important;
                }
                .goods-item img {
                    max-width: 100% !important;
                    height: auto !important;
                }
                /* 隐藏下载APP相关元素 */
                [class*="download"], [class*="app"], .guide-download {
                    display: none !important;
                }
                /* 优化搜索结果显示 */
                .search-result {
                    padding: 5px !important;
                }
            `;
            document.head.appendChild(style);

            console.log('拼多多页面已优化用于iframe显示');
        } catch (error) {
            console.error('优化拼多多页面失败:', error);
        }
    }

    monitorSearchBox() {
        try {
            const searchBox = document.querySelector('.search-input') || 
                            document.querySelector('input[placeholder*="搜索"]');
            if (searchBox) {
                searchBox.addEventListener('input', (e) => {
                    const keyword = e.target.value;
                    if (keyword.length > 0) {
                        console.log('拼多多搜索关键词变化:', keyword);
                    }
                });
            }
        } catch (error) {
            console.error('监听搜索框失败:', error);
        }
    }
}

// 初始化拼多多内容脚本
new PddContentScript();
