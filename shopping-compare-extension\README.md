# 多平台商品搜索对比 Chrome 插件

一个功能强大的Chrome插件，让您能够在同一界面对比京东、淘宝、拼多多的商品搜索结果，支持网格和标签布局切换。

## 🌟 主要功能

### ✅ 核心特性
- **免登录使用**：利用浏览器现有cookie，无需重复登录
- **真实网页嵌入**：使用iframe加载真实电商网站，保证数据准确性
- **同步搜索**：一键搜索三个平台，节省时间
- **灵活布局**：支持网格布局和标签布局切换
- **交互体验**：卡片放大、标签切换等丰富交互

### 🎯 支持平台
- **京东** (jd.com)
- **淘宝** (taobao.com)  
- **拼多多** (pinduoduo.com)

## 🚀 安装步骤

### 方法一：开发者模式安装（推荐）

1. **下载插件文件**
   - 下载整个 `shopping-compare-extension` 文件夹到本地

2. **生成图标文件**
   - 打开 `icons/create-icons.html` 文件
   - 图标文件将自动下载
   - 将下载的图标文件放入 `icons` 文件夹

3. **安装到Chrome**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `shopping-compare-extension` 文件夹
   - 插件安装完成！

### 方法二：打包安装

1. 在Chrome扩展管理页面点击"打包扩展程序"
2. 选择插件文件夹，生成 `.crx` 文件
3. 拖拽 `.crx` 文件到Chrome扩展页面安装

## 📖 使用说明

### 基本操作

1. **启动插件**
   - 点击浏览器工具栏中的插件图标
   - 插件窗口将打开，显示三个平台的页面

2. **搜索商品**
   - 在顶部搜索框输入商品关键词
   - 点击"搜索"按钮
   - 三个平台将同时进行搜索

3. **切换布局**
   - 使用"网格布局/标签布局"下拉框切换显示模式
   - 网格布局：三个平台并排显示
   - 标签布局：通过标签切换平台

### 高级功能

4. **放大查看**
   - 在网格布局中点击"放大"按钮
   - 选中的平台将全屏显示
   - 其他平台显示为侧边栏标签

5. **快速切换**
   - 在放大模式下点击侧边栏标签
   - 快速切换到其他平台
   - 点击"×"关闭放大模式

## 🔧 技术特性

### 架构设计
- **Manifest V3**：使用最新的Chrome扩展标准
- **模块化设计**：清晰的代码结构，易于维护
- **响应式布局**：适配不同屏幕尺寸

### 安全性
- **最小权限原则**：仅请求必要的权限
- **数据隐私**：不收集用户个人信息
- **本地存储**：设置和历史记录仅保存在本地

### 兼容性
- **Chrome 88+**：支持现代Chrome浏览器
- **跨平台**：Windows、macOS、Linux
- **移动适配**：优化移动端显示效果

## 🛠️ 开发说明

### 文件结构
```
shopping-compare-extension/
├── manifest.json          # 插件配置文件
├── popup.html             # 主界面HTML
├── popup.css              # 样式文件
├── popup.js               # 主要逻辑
├── background.js          # 后台脚本
├── content-scripts/       # 内容脚本
│   ├── jd.js             # 京东脚本
│   ├── taobao.js         # 淘宝脚本
│   └── pdd.js            # 拼多多脚本
├── icons/                 # 图标文件
└── README.md             # 说明文档
```

### 自定义开发

1. **修改搜索逻辑**
   - 编辑对应平台的content script文件
   - 调整搜索框选择器和搜索逻辑

2. **添加新平台**
   - 在 `popup.js` 中添加平台配置
   - 创建对应的content script
   - 更新manifest.json权限

3. **界面定制**
   - 修改 `popup.css` 调整样式
   - 编辑 `popup.html` 修改布局

## 🐛 故障排除

### 常见问题

1. **插件无法加载**
   - 检查是否开启开发者模式
   - 确认文件夹结构完整
   - 查看Chrome控制台错误信息

2. **搜索功能异常**
   - 检查网络连接
   - 确认目标网站可正常访问
   - 清除浏览器缓存

3. **iframe显示问题**
   - 某些网站可能阻止iframe嵌入
   - 尝试刷新页面或重新搜索
   - 检查浏览器安全设置

### 调试方法

1. **查看控制台**
   - 右键插件图标 → 检查弹出式窗口
   - 查看Console面板的错误信息

2. **检查权限**
   - 访问 `chrome://extensions/`
   - 确认插件权限设置正确

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 🎉 首次发布
- ✅ 支持京东、淘宝、拼多多三大平台
- ✅ 网格布局和标签布局
- ✅ 同步搜索功能
- ✅ 放大查看模式

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 LICENSE 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果您在使用过程中遇到问题，请：
1. 查看本文档的故障排除部分
2. 在GitHub上提交Issue
3. 发送邮件至开发者邮箱

---

**享受便捷的多平台购物对比体验！** 🛒✨
