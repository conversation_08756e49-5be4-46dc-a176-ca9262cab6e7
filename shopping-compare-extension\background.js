// 后台服务工作脚本
class BackgroundService {
    constructor() {
        this.init();
    }

    init() {
        // 监听扩展安装事件
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstall(details);
        });

        // 监听来自content script的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听标签页更新事件
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });
    }

    handleInstall(details) {
        if (details.reason === 'install') {
            // 首次安装时的初始化
            this.setDefaultSettings();
            console.log('多平台商品搜索对比插件已安装');
        } else if (details.reason === 'update') {
            // 更新时的处理
            console.log('多平台商品搜索对比插件已更新');
        }
    }

    async setDefaultSettings() {
        try {
            await chrome.storage.sync.set({
                layout: 'grid',
                lastKeyword: '',
                autoSearch: true,
                searchHistory: []
            });
        } catch (error) {
            console.error('设置默认配置失败:', error);
        }
    }

    handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'performSearch':
                this.handleSearchRequest(request, sendResponse);
                break;
            case 'getSearchHistory':
                this.getSearchHistory(sendResponse);
                break;
            case 'saveSearchHistory':
                this.saveSearchHistory(request.keyword, sendResponse);
                break;
            case 'clearHistory':
                this.clearSearchHistory(sendResponse);
                break;
            default:
                sendResponse({ error: '未知的操作类型' });
        }
    }

    async handleSearchRequest(request, sendResponse) {
        try {
            const { keyword, platforms } = request;
            
            // 保存搜索历史
            await this.saveSearchHistory(keyword);
            
            // 构建搜索URL
            const searchUrls = {};
            platforms.forEach(platform => {
                searchUrls[platform] = this.buildSearchUrl(platform, keyword);
            });

            sendResponse({ 
                success: true, 
                urls: searchUrls 
            });
        } catch (error) {
            console.error('处理搜索请求失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message 
            });
        }
    }

    buildSearchUrl(platform, keyword) {
        const encodedKeyword = encodeURIComponent(keyword);
        const searchConfigs = {
            jd: `https://search.jd.com/Search?keyword=${encodedKeyword}`,
            taobao: `https://s.taobao.com/search?q=${encodedKeyword}`,
            pdd: `https://mobile.pinduoduo.com/search_result.html?keyword=${encodedKeyword}`
        };
        
        return searchConfigs[platform] || 'about:blank';
    }

    async getSearchHistory(sendResponse) {
        try {
            const result = await chrome.storage.sync.get(['searchHistory']);
            const history = result.searchHistory || [];
            sendResponse({ 
                success: true, 
                history: history.slice(-10) // 只返回最近10条
            });
        } catch (error) {
            console.error('获取搜索历史失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message 
            });
        }
    }

    async saveSearchHistory(keyword, sendResponse = null) {
        try {
            const result = await chrome.storage.sync.get(['searchHistory']);
            let history = result.searchHistory || [];
            
            // 移除重复项
            history = history.filter(item => item.keyword !== keyword);
            
            // 添加新的搜索记录
            history.push({
                keyword: keyword,
                timestamp: Date.now(),
                date: new Date().toLocaleDateString('zh-CN')
            });
            
            // 只保留最近50条记录
            if (history.length > 50) {
                history = history.slice(-50);
            }
            
            await chrome.storage.sync.set({ searchHistory: history });
            
            if (sendResponse) {
                sendResponse({ success: true });
            }
        } catch (error) {
            console.error('保存搜索历史失败:', error);
            if (sendResponse) {
                sendResponse({ 
                    success: false, 
                    error: error.message 
                });
            }
        }
    }

    async clearSearchHistory(sendResponse) {
        try {
            await chrome.storage.sync.set({ searchHistory: [] });
            sendResponse({ success: true });
        } catch (error) {
            console.error('清除搜索历史失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message 
            });
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // 监听标签页状态变化，可以用于优化iframe加载
        if (changeInfo.status === 'complete' && tab.url) {
            // 检查是否是支持的电商网站
            const supportedDomains = [
                'jd.com',
                'taobao.com',
                'pinduoduo.com',
                'yangkeduo.com'
            ];
            
            const isSupported = supportedDomains.some(domain => 
                tab.url.includes(domain)
            );
            
            if (isSupported) {
                // 可以在这里添加额外的处理逻辑
                console.log('检测到支持的电商网站:', tab.url);
            }
        }
    }

    // 工具方法：检查URL是否为电商搜索页面
    isSearchPage(url) {
        const searchPatterns = [
            /search\.jd\.com\/Search/,
            /s\.taobao\.com\/search/,
            /mobile\.pinduoduo\.com\/search_result/
        ];
        
        return searchPatterns.some(pattern => pattern.test(url));
    }

    // 工具方法：从URL中提取搜索关键词
    extractKeywordFromUrl(url) {
        try {
            const urlObj = new URL(url);
            const params = urlObj.searchParams;
            
            // 尝试不同的参数名
            const keywordParams = ['keyword', 'q', 'key', 'search'];
            for (const param of keywordParams) {
                const value = params.get(param);
                if (value) {
                    return decodeURIComponent(value);
                }
            }
        } catch (error) {
            console.error('提取关键词失败:', error);
        }
        
        return null;
    }
}

// 初始化后台服务
new BackgroundService();
