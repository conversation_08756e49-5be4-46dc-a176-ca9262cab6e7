<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多平台商品搜索对比</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 顶部控制栏 -->
        <div class="header">
            <h3>多平台商品搜索对比</h3>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="输入商品关键词..." />
                <button id="searchBtn" class="search-btn">搜索</button>
            </div>
            <div class="layout-controls">
                <select id="layoutSelect" class="layout-select">
                    <option value="grid">网格布局</option>
                    <option value="tab">标签布局</option>
                </select>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="status-area">
                <p id="statusMessage">插件已准备就绪</p>
            </div>

            <div class="platform-links">
                <h4>支持的购物平台：</h4>
                <div class="platform-list">
                    <div class="platform-item">
                        <span class="platform-name">🛒 京东</span>
                        <span class="platform-desc">综合电商平台</span>
                    </div>
                    <div class="platform-item">
                        <span class="platform-name">🛍️ 淘宝</span>
                        <span class="platform-desc">阿里巴巴旗下购物网站</span>
                    </div>
                    <div class="platform-item">
                        <span class="platform-name">🎁 拼多多</span>
                        <span class="platform-desc">社交电商平台</span>
                    </div>
                </div>
            </div>

            <div class="instructions">
                <h4>使用说明：</h4>
                <ol>
                    <li>在上方搜索框输入商品关键词</li>
                    <li>点击"搜索"按钮</li>
                    <li>系统将在新标签页中打开三个平台的搜索结果</li>
                    <li>您可以在不同标签页间切换对比商品</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
