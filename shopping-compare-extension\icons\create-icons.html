<!DOCTYPE html>
<html>
<head>
    <title>生成插件图标</title>
</head>
<body>
    <canvas id="canvas" width="128" height="128"></canvas>
    <script>
        // 创建插件图标
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 背景渐变
        const gradient = ctx.createLinearGradient(0, 0, 128, 128);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 128, 128);
        
        // 绘制购物车图标
        ctx.fillStyle = 'white';
        ctx.font = 'bold 60px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('🛒', 64, 64);
        
        // 添加对比符号
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = 'bold 20px Arial';
        ctx.fillText('VS', 64, 100);
        
        // 下载不同尺寸的图标
        function downloadIcon(size) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = size;
            tempCanvas.height = size;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(canvas, 0, 0, size, size);
            
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = tempCanvas.toDataURL();
            link.click();
        }
        
        // 自动下载所有尺寸的图标
        setTimeout(() => {
            [16, 32, 48, 128].forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }, 1000);
    </script>
    <p>图标将自动下载，请将下载的图标文件放入 icons 文件夹中</p>
</body>
</html>
