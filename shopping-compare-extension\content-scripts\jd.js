// 京东网站内容脚本
class JDContentScript {
    constructor() {
        this.platform = 'jd';
        this.searchSelectors = {
            searchBox: '#key',
            searchButton: '.button',
            searchForm: '#search-2014'
        };
        this.init();
    }

    init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupContentScript();
            });
        } else {
            this.setupContentScript();
        }
    }

    setupContentScript() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });

        // 优化页面显示（移除不必要的元素）
        this.optimizePageForIframe();

        // 监听搜索框变化
        this.monitorSearchBox();

        console.log('京东内容脚本已加载');
    }

    handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'performSearch':
                this.performSearch(request.keyword, sendResponse);
                break;
            case 'getPageInfo':
                this.getPageInfo(sendResponse);
                break;
            case 'optimizePage':
                this.optimizePageForIframe();
                sendResponse({ success: true });
                break;
            default:
                sendResponse({ error: '未知操作' });
        }
    }

    performSearch(keyword, sendResponse) {
        try {
            const searchBox = document.querySelector(this.searchSelectors.searchBox);
            const searchButton = document.querySelector(this.searchSelectors.searchButton);

            if (searchBox) {
                // 清空并输入新关键词
                searchBox.value = '';
                searchBox.focus();
                
                // 模拟用户输入
                this.simulateTyping(searchBox, keyword);
                
                // 触发搜索
                setTimeout(() => {
                    if (searchButton) {
                        searchButton.click();
                    } else {
                        // 如果没有找到按钮，尝试提交表单
                        const form = document.querySelector(this.searchSelectors.searchForm);
                        if (form) {
                            form.submit();
                        } else {
                            // 最后尝试按回车键
                            const enterEvent = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                bubbles: true
                            });
                            searchBox.dispatchEvent(enterEvent);
                        }
                    }
                    
                    sendResponse({ 
                        success: true, 
                        message: '搜索已执行',
                        platform: this.platform 
                    });
                }, 500);
            } else {
                sendResponse({ 
                    success: false, 
                    error: '未找到搜索框',
                    platform: this.platform 
                });
            }
        } catch (error) {
            console.error('京东搜索执行失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message,
                platform: this.platform 
            });
        }
    }

    simulateTyping(element, text) {
        // 模拟真实的用户输入行为
        element.value = '';
        
        for (let i = 0; i < text.length; i++) {
            setTimeout(() => {
                element.value += text[i];
                
                // 触发输入事件
                const inputEvent = new Event('input', { bubbles: true });
                element.dispatchEvent(inputEvent);
                
                const changeEvent = new Event('change', { bubbles: true });
                element.dispatchEvent(changeEvent);
            }, i * 50); // 每个字符间隔50ms
        }
    }

    getPageInfo(sendResponse) {
        try {
            const info = {
                platform: this.platform,
                url: window.location.href,
                title: document.title,
                isSearchPage: this.isSearchPage(),
                currentKeyword: this.getCurrentKeyword(),
                productCount: this.getProductCount(),
                hasResults: this.hasSearchResults()
            };
            
            sendResponse({ success: true, info });
        } catch (error) {
            console.error('获取页面信息失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message 
            });
        }
    }

    isSearchPage() {
        return window.location.href.includes('search.jd.com') || 
               window.location.href.includes('Search');
    }

    getCurrentKeyword() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('keyword') || '';
        } catch (error) {
            return '';
        }
    }

    getProductCount() {
        try {
            const countElement = document.querySelector('.total');
            if (countElement) {
                const text = countElement.textContent;
                const match = text.match(/(\d+)/);
                return match ? parseInt(match[1]) : 0;
            }
            return 0;
        } catch (error) {
            return 0;
        }
    }

    hasSearchResults() {
        const resultSelectors = [
            '#J_goodsList .gl-item',
            '.gl-warp .gl-item',
            '.p-img'
        ];
        
        return resultSelectors.some(selector => 
            document.querySelectorAll(selector).length > 0
        );
    }

    optimizePageForIframe() {
        try {
            // 移除或隐藏不必要的元素以优化iframe显示
            const elementsToHide = [
                '.header-2014',  // 顶部导航
                '.footer',       // 底部
                '.J_promotion',  // 促销信息
                '.ad',          // 广告
                '.popup',       // 弹窗
                '.float-layer', // 浮层
                '#ttbar-login', // 登录条
                '.toolbar'      // 工具栏
            ];

            elementsToHide.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.style.display = 'none';
                });
            });

            // 调整页面样式以适应iframe
            const style = document.createElement('style');
            style.textContent = `
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                .w {
                    width: 100% !important;
                    min-width: auto !important;
                }
                #container {
                    width: 100% !important;
                }
                .main {
                    width: 100% !important;
                }
            `;
            document.head.appendChild(style);

            console.log('京东页面已优化用于iframe显示');
        } catch (error) {
            console.error('优化京东页面失败:', error);
        }
    }

    monitorSearchBox() {
        try {
            const searchBox = document.querySelector(this.searchSelectors.searchBox);
            if (searchBox) {
                // 监听搜索框的变化
                searchBox.addEventListener('input', (e) => {
                    const keyword = e.target.value;
                    if (keyword.length > 0) {
                        // 可以在这里添加搜索建议等功能
                        console.log('京东搜索关键词变化:', keyword);
                    }
                });
            }
        } catch (error) {
            console.error('监听搜索框失败:', error);
        }
    }
}

// 初始化京东内容脚本
new JDContentScript();
