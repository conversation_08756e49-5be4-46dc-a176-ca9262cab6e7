// 淘宝网站内容脚本
class TaobaoContentScript {
    constructor() {
        this.platform = 'taobao';
        this.searchSelectors = {
            searchBox: '#q',
            searchButton: '.btn-search',
            searchForm: '#J_TSearchForm'
        };
        this.init();
    }

    init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupContentScript();
            });
        } else {
            this.setupContentScript();
        }
    }

    setupContentScript() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });

        // 优化页面显示
        this.optimizePageForIframe();

        // 监听搜索框变化
        this.monitorSearchBox();

        console.log('淘宝内容脚本已加载');
    }

    handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'performSearch':
                this.performSearch(request.keyword, sendResponse);
                break;
            case 'getPageInfo':
                this.getPageInfo(sendResponse);
                break;
            case 'optimizePage':
                this.optimizePageForIframe();
                sendResponse({ success: true });
                break;
            default:
                sendResponse({ error: '未知操作' });
        }
    }

    performSearch(keyword, sendResponse) {
        try {
            // 淘宝可能有多个搜索框，尝试不同的选择器
            const searchSelectors = ['#q', '.search-combobox-input', '#mq'];
            let searchBox = null;
            
            for (const selector of searchSelectors) {
                searchBox = document.querySelector(selector);
                if (searchBox) break;
            }

            if (searchBox) {
                // 清空并输入新关键词
                searchBox.value = '';
                searchBox.focus();
                
                // 模拟用户输入
                this.simulateTyping(searchBox, keyword);
                
                // 触发搜索
                setTimeout(() => {
                    const searchButton = document.querySelector(this.searchSelectors.searchButton) ||
                                       document.querySelector('.search-button') ||
                                       document.querySelector('#J_TSearchForm button');
                    
                    if (searchButton) {
                        searchButton.click();
                    } else {
                        // 尝试提交表单
                        const form = document.querySelector(this.searchSelectors.searchForm) ||
                                   document.querySelector('form[action*="search"]');
                        if (form) {
                            form.submit();
                        } else {
                            // 按回车键
                            const enterEvent = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                bubbles: true
                            });
                            searchBox.dispatchEvent(enterEvent);
                        }
                    }
                    
                    sendResponse({ 
                        success: true, 
                        message: '搜索已执行',
                        platform: this.platform 
                    });
                }, 500);
            } else {
                sendResponse({ 
                    success: false, 
                    error: '未找到搜索框',
                    platform: this.platform 
                });
            }
        } catch (error) {
            console.error('淘宝搜索执行失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message,
                platform: this.platform 
            });
        }
    }

    simulateTyping(element, text) {
        element.value = '';
        
        for (let i = 0; i < text.length; i++) {
            setTimeout(() => {
                element.value += text[i];
                
                // 触发各种事件以确保淘宝能识别输入
                const events = ['input', 'change', 'keyup'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    element.dispatchEvent(event);
                });
            }, i * 50);
        }
    }

    getPageInfo(sendResponse) {
        try {
            const info = {
                platform: this.platform,
                url: window.location.href,
                title: document.title,
                isSearchPage: this.isSearchPage(),
                currentKeyword: this.getCurrentKeyword(),
                productCount: this.getProductCount(),
                hasResults: this.hasSearchResults()
            };
            
            sendResponse({ success: true, info });
        } catch (error) {
            console.error('获取页面信息失败:', error);
            sendResponse({ 
                success: false, 
                error: error.message 
            });
        }
    }

    isSearchPage() {
        return window.location.href.includes('s.taobao.com') || 
               window.location.href.includes('search');
    }

    getCurrentKeyword() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('q') || urlParams.get('keyword') || '';
        } catch (error) {
            return '';
        }
    }

    getProductCount() {
        try {
            // 淘宝搜索结果数量的选择器
            const countSelectors = [
                '.total',
                '.search-result-count',
                '.result-count'
            ];
            
            for (const selector of countSelectors) {
                const countElement = document.querySelector(selector);
                if (countElement) {
                    const text = countElement.textContent;
                    const match = text.match(/(\d+)/);
                    if (match) {
                        return parseInt(match[1]);
                    }
                }
            }
            return 0;
        } catch (error) {
            return 0;
        }
    }

    hasSearchResults() {
        const resultSelectors = [
            '.item',
            '.product',
            '.grid-item',
            '[data-category="auctions"]'
        ];
        
        return resultSelectors.some(selector => 
            document.querySelectorAll(selector).length > 0
        );
    }

    optimizePageForIframe() {
        try {
            // 移除或隐藏不必要的元素
            const elementsToHide = [
                '.site-nav',     // 顶部导航
                '.footer',       // 底部
                '.J_Module',     // 模块
                '.ad',          // 广告
                '.popup',       // 弹窗
                '.float-layer', // 浮层
                '.login-info',  // 登录信息
                '.toolbar',     // 工具栏
                '.header',      // 头部
                '#header'       // 头部
            ];

            elementsToHide.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.style.display = 'none';
                });
            });

            // 调整页面样式
            const style = document.createElement('style');
            style.textContent = `
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    min-width: auto !important;
                }
                .container, .main-wrap {
                    width: 100% !important;
                    min-width: auto !important;
                    margin: 0 !important;
                    padding: 10px !important;
                }
                .grid {
                    width: 100% !important;
                }
                .items-list {
                    width: 100% !important;
                }
            `;
            document.head.appendChild(style);

            console.log('淘宝页面已优化用于iframe显示');
        } catch (error) {
            console.error('优化淘宝页面失败:', error);
        }
    }

    monitorSearchBox() {
        try {
            const searchBox = document.querySelector('#q') || 
                            document.querySelector('.search-combobox-input');
            if (searchBox) {
                searchBox.addEventListener('input', (e) => {
                    const keyword = e.target.value;
                    if (keyword.length > 0) {
                        console.log('淘宝搜索关键词变化:', keyword);
                    }
                });
            }
        } catch (error) {
            console.error('监听搜索框失败:', error);
        }
    }
}

// 初始化淘宝内容脚本
new TaobaoContentScript();
