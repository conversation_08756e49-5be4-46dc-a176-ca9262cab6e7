/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    width: 380px;
    height: 480px;
    min-width: 380px;
    max-width: 380px;
    min-height: 480px;
    max-height: 480px;
    overflow: hidden;
    font-size: 14px;
    line-height: 1.4;
}

.container {
    width: 380px;
    height: 480px;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 顶部控制栏 */
.header {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    flex-shrink: 0;
}

.header h3 {
    margin: 0;
    text-align: center;
    font-size: 15px;
    font-weight: 600;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

#searchInput {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 20px;
    font-size: 13px;
    outline: none;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

#searchInput:focus {
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.search-btn {
    padding: 8px 16px;
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #ff5252;
    transform: translateY(-1px);
}

.layout-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.layout-select {
    padding: 8px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    cursor: pointer;
    outline: none;
}

.layout-select option {
    background: #333;
    color: white;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    min-height: 0;
}

.status-area {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    border-left: 3px solid #007bff;
}

#statusMessage {
    margin: 0;
    color: #495057;
    font-weight: 500;
    font-size: 13px;
}

.platform-links {
    margin-bottom: 16px;
}

.platform-links h4 {
    margin: 0 0 12px 0;
    color: #343a40;
    font-size: 14px;
    font-weight: 600;
}

.platform-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.platform-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.platform-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-1px);
}

.platform-name {
    font-weight: 600;
    color: #495057;
    font-size: 13px;
}

.platform-desc {
    font-size: 11px;
    color: #6c757d;
}

.instructions {
    background: #e7f3ff;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.instructions h4 {
    margin: 0 0 8px 0;
    color: #0056b3;
    font-size: 13px;
    font-weight: 600;
}

.instructions ol {
    margin: 0;
    padding-left: 16px;
}

.instructions li {
    margin-bottom: 4px;
    color: #495057;
    font-size: 12px;
    line-height: 1.3;
}

/* 响应式设计 */
@media (max-width: 400px) {
    body, .container {
        width: 350px;
        min-width: 350px;
        max-width: 350px;
    }

    .header {
        padding: 10px 12px;
    }

    .main-content {
        padding: 12px;
    }

    .search-container {
        gap: 6px;
    }

    #searchInput {
        font-size: 12px;
        padding: 6px 10px;
    }

    .search-btn {
        font-size: 12px;
        padding: 6px 12px;
    }
}
